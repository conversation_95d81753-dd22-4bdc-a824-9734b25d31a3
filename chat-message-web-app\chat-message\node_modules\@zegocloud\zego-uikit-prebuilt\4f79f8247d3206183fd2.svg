<?xml version="1.0" encoding="utf-8"?>
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" style="margin: auto; background: none; display: block; shape-rendering: auto;" width="116px" height="116px" viewBox="0 0 100 100" preserveAspectRatio="xMidYMid">
<circle cx="30" cy="50" fill="#0055ff" r="20">
  <animate attributeName="cx" repeatCount="indefinite" dur="1.0204081632653061s" keyTimes="0;0.5;1" values="30;70;30" begin="-0.5102040816326531s"></animate>
</circle>
<circle cx="70" cy="50" fill="#4698f0" r="20">
  <animate attributeName="cx" repeatCount="indefinite" dur="1.0204081632653061s" keyTimes="0;0.5;1" values="30;70;30" begin="0s"></animate>
</circle>
<circle cx="30" cy="50" fill="#0055ff" r="20">
  <animate attributeName="cx" repeatCount="indefinite" dur="1.0204081632653061s" keyTimes="0;0.5;1" values="30;70;30" begin="-0.5102040816326531s"></animate>
  <animate attributeName="fill-opacity" values="0;0;1;1" calcMode="discrete" keyTimes="0;0.499;0.5;1" dur="1.0204081632653061s" repeatCount="indefinite"></animate>
</circle>
<!-- [ldio] generated by https://loading.io/ --></svg>