export enum CallType {
    VIDEO = 'VIDEO',
    AUDIO = 'AUDIO'
}

export enum CallStatus {
    INITIATED = 'INITIATED',
    RINGING = 'RINGING',
    ACCEPTED = 'ACCEPTED',
    REJECTED = 'REJECTED',
    ENDED = 'ENDED',
    MISSED = 'MISSED'
}

export interface VideoCallResponse {
    callId: number;
    roomId: string;
    token: string;
    status: CallStatus;
    roomConfig: {
        scenario: string;
        maxUsers: number;
        enableVideo: boolean;
        enableAudio: boolean;
        enableScreenSharing: boolean;
        enableChat: boolean;
        enableRecording: boolean;
    };
}

export interface InitiateCallRequest {
    conversationId: number;
    callType: CallType;
}

export interface VideoCallInviteMessage {
    callId: number;
    conversationId: number;
    callerId: number;
    callerName: string;
    callerAvatar: string;
    roomId: string;
    callType: CallType;
    status: CallStatus;
    roomConfig: {
        scenario: string;
        maxUsers: number;
        enableVideo: boolean;
        enableAudio: boolean;
        enableScreenSharing: boolean;
        enableChat: boolean;
        enableRecording: boolean;
    };
}

export interface VideoCallAcceptedMessage {
    callId: number;
    conversationId: number;
    accepterId: number;
    roomId: string;
    status: CallStatus;
    roomConfig: {
        scenario: string;
        maxUsers: number;
        enableVideo: boolean;
        enableAudio: boolean;
        enableScreenSharing: boolean;
        enableChat: boolean;
        enableRecording: boolean;
    };
}

export interface VideoCallRejectedMessage {
    callId: number;
    conversationId: number;
    rejectedById: number;
    status: CallStatus;
}

export interface VideoCallEndedMessage {
    callId: number;
    conversationId: number;
    endedById: number;
    durationSeconds: number;
    status: CallStatus;
}

export interface VideoCallHistoryResponse {
    callId: number;
    callType: CallType;
    status: CallStatus;
    durationSeconds: number;
    createdAt: string;
    startedAt: string;
    endedAt: string;
    callerName: string;
    callerAvatar: string;
}

export interface VideoCallStatsResponse {
    totalCalls: number;
    totalDurationSeconds: number;
    videoCalls: number;
    audioCalls: number;
}

export interface TokenResponse {
    token: string;
}
