/**
 * Generate a unique and persistent device ID for the current browser/device
 * The ID will be stored in localStorage and reused across sessions
 */
export const getDeviceId = (): string => {
    // Check if deviceId already exists in localStorage
    let deviceId = localStorage.getItem('deviceId');
    
    if (!deviceId) {
        // Create a unique device ID using available browser APIs
        const userAgent = window.navigator.userAgent;
        const language = window.navigator.language;
        const screenResolution = `${window.screen.width}x${window.screen.height}`;
        const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
        const timestamp = Date.now();
        
        // Generate a random component for additional uniqueness
        const randomComponent = Math.random().toString(36).substring(2, 15) + 
                              Math.random().toString(36).substring(2, 15);
        
        // Create a hash-like string from browser fingerprint
        const fingerprint = btoa(
            `${userAgent}-${language}-${screenResolution}-${timezone}-${timestamp}-${randomComponent}`
        ).replace(/[^a-zA-Z0-9]/g, '').substring(0, 32);
        
        deviceId = `device_${fingerprint}`;
        
        // Store in localStorage for persistence across sessions
        localStorage.setItem('deviceId', deviceId);
    }
    
    return deviceId;
};

/**
 * Clear the stored device ID (useful for testing or reset functionality)
 */
export const clearDeviceId = (): void => {
    localStorage.removeItem('deviceId');
};

/**
 * Get device information for debugging purposes
 */
export const getDeviceInfo = () => {
    return {
        deviceId: getDeviceId(),
        userAgent: window.navigator.userAgent,
        language: window.navigator.language,
        screenResolution: `${window.screen.width}x${window.screen.height}`,
        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
        cookieEnabled: window.navigator.cookieEnabled,
        onLine: window.navigator.onLine
    };
};
