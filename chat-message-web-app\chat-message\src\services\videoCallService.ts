import axios from 'axios';
import {
    VideoCallResponse,
    InitiateCallRequest,
    VideoCallHistoryResponse,
    VideoCallStatsResponse,
    TokenResponse
} from '@/types/videoCall';

export class VideoCallService {

    /**
     * Initiate a new video/audio call
     */
    async initiateCall(request: InitiateCallRequest): Promise<VideoCallResponse> {
        const response = await axios.post<VideoCallResponse>('/api/video-calls/initiate', request);
        return response.data;
    }

    /**
     * Accept an incoming call
     */
    async acceptCall(callId: number): Promise<VideoCallResponse> {
        const response = await axios.post<VideoCallResponse>(
            `/api/video-calls/${callId}/accept`
        );
        return response.data;
    }

    /**
     * Reject an incoming call
     */
    async rejectCall(callId: number): Promise<void> {
        await axios.post(`/api/video-calls/${callId}/reject`);
    }

    /**
     * End an ongoing call
     */
    async endCall(callId: number): Promise<void> {
        await axios.post(`/api/video-calls/${callId}/end`);
    }

    /**
     * Generate a new token for a user to join a call
     */
    async generateToken(callId: number): Promise<string> {
        const response = await axios.get<TokenResponse>(
            `/api/video-calls/${callId}/token`
        );
        return response.data.token;
    }

    /**
     * Get call history for a conversation
     */
    async getCallHistory(conversationId: number): Promise<VideoCallHistoryResponse[]> {
        const response = await axios.get<VideoCallHistoryResponse[]>(
            `/api/video-calls/history/${conversationId}`
        );
        return response.data;
    }

    /**
     * Get call statistics for a conversation
     */
    async getCallStats(conversationId: number): Promise<VideoCallStatsResponse> {
        const response = await axios.get<VideoCallStatsResponse>(
            `/api/video-calls/stats/${conversationId}`
        );
        return response.data;
    }
}

export const videoCallService = new VideoCallService();
