import { apiService } from './api';
import { 
    VideoCallResponse, 
    InitiateCallRequest, 
    AcceptCallRequest, 
    RejectCallRequest, 
    EndCallRequest,
    VideoCallHistoryResponse,
    VideoCallStatsResponse,
    TokenResponse
} from '@/types/videoCall';

export class VideoCallService {
    
    /**
     * Initiate a new video/audio call
     */
    async initiateCall(request: InitiateCallRequest): Promise<VideoCallResponse> {
        const response = await apiService.post<VideoCallResponse>('/api/video-calls/initiate', request);
        return response.data;
    }

    /**
     * Accept an incoming call
     */
    async acceptCall(callId: number, userId: number): Promise<VideoCallResponse> {
        const response = await apiService.post<VideoCallResponse>(
            `/api/video-calls/${callId}/accept`,
            { userId }
        );
        return response.data;
    }

    /**
     * Reject an incoming call
     */
    async rejectCall(callId: number, userId: number): Promise<void> {
        await apiService.post(`/api/video-calls/${callId}/reject`, { userId });
    }

    /**
     * End an ongoing call
     */
    async endCall(callId: number, userId: number): Promise<void> {
        await apiService.post(`/api/video-calls/${callId}/end`, { userId });
    }

    /**
     * Generate a new token for a user to join a call
     */
    async generateToken(callId: number, userId: number): Promise<string> {
        const response = await apiService.get<TokenResponse>(
            `/api/video-calls/${callId}/token?userId=${userId}`
        );
        return response.data.token;
    }

    /**
     * Get call history for a conversation
     */
    async getCallHistory(conversationId: number): Promise<VideoCallHistoryResponse[]> {
        const response = await apiService.get<VideoCallHistoryResponse[]>(
            `/api/video-calls/history/${conversationId}`
        );
        return response.data;
    }

    /**
     * Get call statistics for a conversation
     */
    async getCallStats(conversationId: number): Promise<VideoCallStatsResponse> {
        const response = await apiService.get<VideoCallStatsResponse>(
            `/api/video-calls/stats/${conversationId}`
        );
        return response.data;
    }
}

export const videoCallService = new VideoCallService();
