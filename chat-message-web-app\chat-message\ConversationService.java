package vn.khanhduc.chatmessage.service;

import org.springframework.messaging.simp.SimpMessagingTemplate;
import vn.khanhduc.chatmessage.dto.request.ConversationCreationRequest;
import vn.khanhduc.chatmessage.dto.response.ConversationCreationResponse;
import vn.khanhduc.chatmessage.entity.Conversation;
import vn.khanhduc.chatmessage.entity.ParticipantInfo;
import vn.khanhduc.chatmessage.entity.User;
import vn.khanhduc.chatmessage.enums.ConversationType;
import vn.khanhduc.chatmessage.exception.AppException;
import vn.khanhduc.chatmessage.exception.ErrorCode;
import vn.khanhduc.chatmessage.mapper.ConversationMapper;
import vn.khanhduc.chatmessage.repository.ConversationRepository;
import vn.khanhduc.chatmessage.repository.UserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j(topic = "CONVERSATION-SERVICE")
public class ConversationService {

    private final ConversationRepository conversationRepository;
    private final UserRepository userRepository;
    private final SimpMessagingTemplate simpMessagingTemplate;

    @Transactional
    public ConversationCreationResponse create(ConversationCreationRequest request) {
        
        String userId = SecurityContextHolder.getContext()
                .getAuthentication().getName();

        User sender = userRepository.findById(userId)
                .orElseThrow(() -> new AppException(ErrorCode.USER_NOT_FOUND));

        List<String> participantIds = request.getParticipantIds();

        if(!participantIds.contains(sender.getId())) {
            participantIds.add(sender.getId());
        }

        List<User> participants = userRepository.findAllById(participantIds);

        if(participants.size() != participantIds.size()) {
            throw new AppException(ErrorCode.PARTICIPANT_INVALID);
        }

        String participantHash = participantIds.size() > 1
                ? participantIds.stream()
                    .distinct()
                    .sorted().collect(Collectors.joining("_"))
                : participantIds.getFirst();

        // PRIVATE CONVERSATION CHECK
        if(request.getConversationType() == ConversationType.PRIVATE) {
            if (participantIds.size() > 2) {
                throw new AppException(ErrorCode.PRIVATE_CONVERSATION_MAX_TWO_PARTICIPANTS);
            }
            Optional<Conversation> existingConversation = conversationRepository.findByParticipantHash(participantHash);
            if(existingConversation.isPresent()) {
                log.info("Private conversation already exists for hash: {}", participantHash);
                return ConversationMapper.mapToConversationResponse(existingConversation.get(), userId);
            }
        }

        // GROUP CONVERSATION VALIDATION
        if (request.getConversationType() == ConversationType.GROUP) {
            if (request.getConversationName() == null || request.getConversationName().trim().isEmpty()) {
                throw new AppException(ErrorCode.CONVERSATION_NAME_REQUIRED);
            }
            if (participantIds.size() < 3) {
                throw new AppException(ErrorCode.GROUP_CONVERSATION_MINIMUM_THREE_PARTICIPANTS);
            }
        }

        // CREATE PARTICIPANT INFOS
        List<ParticipantInfo> participantInfos = participants.stream()
                .map(user -> ParticipantInfo.builder()
                        .user(user)
                        .conversation(null) // Will be set after conversation creation
                        .joinedAt(LocalDateTime.now())
                        .build())
                .toList();

        // CREATE NEW CONVERSATION
        Conversation conversation = Conversation.builder()
                .name(request.getConversationName())
                .conversationType(request.getConversationType())
                .participantHash(request.getConversationType() == ConversationType.PRIVATE ? participantHash : null)
                .participants(participantInfos)
                .conversationAvatar(request.getConversationType() == ConversationType.GROUP
                        && request.getConversationAvatar() != null ? request.getConversationAvatar() : null)
                .lastMessageAt(LocalDateTime.now())
                .createdAt(LocalDateTime.now())
                .build();

        // Set conversation reference in participant infos
        participantInfos.forEach(participantInfo ->
                participantInfo.setConversation(conversation));

        // Save conversation
        Conversation savedConversation = conversationRepository.save(conversation);
        log.info("Created new {} conversation with ID: {}", 
                request.getConversationType(), savedConversation.getId());

        // Create response
        ConversationCreationResponse conversationResponse =
                ConversationMapper.mapToConversationResponse(savedConversation, userId);

        // BROADCAST TO ALL PARTICIPANTS VIA WEBSOCKET - GỘP TRỰC TIẾP
        try {
            ConversationUpdateEvent event = ConversationUpdateEvent.builder()
                    .type("CONVERSATION_CREATED")
                    .conversation(conversationResponse)
                    .timestamp(System.currentTimeMillis())
                    .build();

            participantIds.forEach(participantId -> {
                try {
                    simpMessagingTemplate.convertAndSendToUser(
                            participantId,
                            "/queue/conversation-updates",
                            event
                    );
                    log.debug("Sent CONVERSATION_CREATED event to user: {}", participantId);
                } catch (Exception e) {
                    log.error("Failed to send WebSocket message to user {}: {}", participantId, e.getMessage());
                }
            });

            log.info("Broadcasted CONVERSATION_CREATED event for conversation: {} to {} participants",
                    conversationResponse.getId(), participantIds.size());
        } catch (Exception e) {
            log.error("Failed to broadcast conversation created: {}", e.getMessage(), e);
        }

        return conversationResponse;
    }

    public List<ConversationCreationResponse> myConversation() {
        String userId = SecurityContextHolder.getContext()
                .getAuthentication().getName();

        User currentUser = userRepository.findById(userId)
                .orElseThrow(() -> new AppException(ErrorCode.USER_NOT_FOUND));

        List<Conversation> conversations = conversationRepository.findByParticipantsUserId(currentUser.getId());

        return conversations.stream()
                .map(conversation -> ConversationMapper.mapToConversationResponse(conversation, userId))
                .sorted((a, b) -> b.getCreatedAt().compareTo(a.getCreatedAt())) // Sort by newest first
                .toList();
    }

    @Transactional
    public void deleteConversation(String id) {
        String userId = SecurityContextHolder.getContext()
                .getAuthentication().getName();
                
        Conversation conversation = conversationRepository.findById(id)
                .orElseThrow(() -> new AppException(ErrorCode.CONVERSATION_NOT_FOUND));
                
        // Get participant IDs before deletion for WebSocket broadcast
        List<String> participantIds = conversation.getParticipants().stream()
                .map(participantInfo -> participantInfo.getUser().getId())
                .toList();
                
        ConversationCreationResponse conversationResponse = 
                ConversationMapper.mapToConversationResponse(conversation, userId);
        
        conversationRepository.delete(conversation);
        log.info("Conversation deleted: {}", id);

        // BROADCAST DELETION TO ALL PARTICIPANTS - GỘP TRỰC TIẾP
        try {
            ConversationUpdateEvent event = ConversationUpdateEvent.builder()
                    .type("CONVERSATION_DELETED")
                    .conversation(conversationResponse)
                    .timestamp(System.currentTimeMillis())
                    .build();

            participantIds.forEach(participantId -> {
                try {
                    simpMessagingTemplate.convertAndSendToUser(
                            participantId,
                            "/queue/conversation-updates",
                            event
                    );
                    log.debug("Sent CONVERSATION_DELETED event to user: {}", participantId);
                } catch (Exception e) {
                    log.error("Failed to send WebSocket message to user {}: {}", participantId, e.getMessage());
                }
            });

            log.info("Broadcasted CONVERSATION_DELETED event for conversation: {} to {} participants",
                    conversationResponse.getId(), participantIds.size());
        } catch (Exception e) {
            log.error("Failed to broadcast conversation deleted: {}", e.getMessage(), e);
        }
    }



    // DTO for WebSocket events
    @lombok.Builder
    @lombok.Data
    public static class ConversationUpdateEvent {
        private String type; // CONVERSATION_CREATED, CONVERSATION_UPDATED, CONVERSATION_DELETED
        private ConversationCreationResponse conversation;
        private long timestamp;
    }
}
