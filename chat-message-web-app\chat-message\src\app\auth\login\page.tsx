'use client';
import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import toast from 'react-hot-toast';
import { ApiService } from '@/api/axios';

export default function LoginPage() {
    const [email, setEmail] = useState('');
    const [password, setPassword] = useState('');
    const router = useRouter();

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        try {
            const data = await ApiService.login(email, password);
            localStorage.setItem('accessToken', data.accessToken);
            localStorage.setItem('refreshToken', data.refreshToken);
            toast.success('Đăng nhập thành công! Đang chuyển hướng...', {
                duration: 500,
            });
            setTimeout(() => router.push('/chat'), 500);
        } catch (error: any) {
            const errorMessage = error.response?.data?.message || 'Đăng nhập thất bại';
            toast.error(errorMessage, {
                duration: 3000,
            });
        }
    };

    const getDeviceId = () => {
        const userAgent = window.navigator.userAgent;
        const platform = window.navigator.platform;
        const randomString = Math.random().toString(20).substring(2, 14) + Math.random().toString(20).substring(2, 14);

        const deviceID = `${userAgent}-${platform}-${randomString}`;
        console.log("deviceID", deviceID);
    }

    useEffect(() => {
        getDeviceId();
    }, []);

    return (
        <div className="min-h-screen flex items-center justify-center bg-gray-100 p-4">
            <div className="max-w-md w-full space-y-8 p-8 bg-white rounded-2xl shadow-xl transform transition-all duration-300">
                <div className="flex flex-col items-center gap-3">
                    <img src="/next.svg" alt="Logo" className="w-16 h-16 mb-2 transform hover:scale-105 transition-transform duration-300" />
                    <h2 className="text-4xl font-bold text-gray-900 tracking-tight">Đăng Nhập</h2>
                    <p className="text-gray-600 text-sm">Chào mừng bạn trở lại! Hãy đăng nhập để tiếp tục.</p>
                </div>
                <form className="space-y-6" onSubmit={handleSubmit}>
                    <div className="space-y-5">
                        <div>
                            <label htmlFor="email" className="block text-sm font-semibold text-gray-800 mb-2">Email</label>
                            <input
                                id="email"
                                name="email"
                                type="email"
                                required
                                className="block w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-gray-500 focus:border-gray-500 bg-white text-gray-900 placeholder-gray-400 transition-all duration-200"
                                placeholder="Nhập email của bạn"
                                value={email}
                                onChange={(e) => setEmail(e.target.value)}
                            />
                        </div>
                        <div>
                            <label htmlFor="password" className="block text-sm font-semibold text-gray-800 mb-2">Mật khẩu</label>
                            <input
                                id="password"
                                name="password"
                                type="password"
                                required
                                className="block w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-gray-500 focus:border-gray-500 bg-white text-gray-900 placeholder-gray-400 transition-all duration-200"
                                placeholder="Nhập mật khẩu"
                                value={password}
                                onChange={(e) => setPassword(e.target.value)}
                            />
                        </div>
                    </div>
                    <button
                        type="submit"
                        className="w-full py-3 rounded-lg bg-gray-900 text-white font-semibold text-lg shadow-lg hover:bg-gray-800 focus:ring-4 focus:ring-gray-300 transition-all duration-300 transform hover:scale-105"
                    >
                        Đăng Nhập
                    </button>
                    <div className="text-center text-sm mt-3">
                        <span className="text-gray-600">Chưa có tài khoản? </span>
                        <Link href="/auth/register" className="font-semibold text-gray-700 hover:text-gray-900 hover:underline transition-colors duration-200">Đăng ký ngay</Link>
                    </div>
                </form>
            </div>
        </div>
    );
}