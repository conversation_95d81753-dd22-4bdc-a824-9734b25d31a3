'use client';

import React, { useEffect, useRef, useState } from 'react';
import { ZegoUIKitPrebuilt } from '@zegocloud/zego-uikit-prebuilt';
import { ZEGO_CONFIG } from '@/utils/deviceUtils';
import axios from 'axios';
import { CallStatus, VideoCallResponse } from '@/types/videoCall';

interface VideoCallModalProps {
    isOpen: boolean;
    onClose: () => void;
    callData: VideoCallResponse | null;
    conversationId: number;
    isIncoming?: boolean;
    callerName?: string;
    callerAvatar?: string;
}

const VideoCallModal: React.FC<VideoCallModalProps> = ({
    isOpen,
    onClose,
    callData,
    conversationId,
    isIncoming = false,
    callerName,
    callerAvatar
}) => {
    const [callStatus, setCallStatus] = useState<CallStatus>(CallStatus.INITIATED);
    const [isJoining, setIsJoining] = useState(false);
    const videoContainerRef = useRef<HTMLDivElement>(null);
    const zegoInstanceRef = useRef<any>(null);

    useEffect(() => {
        if (callData) {
            setCallStatus(callData.status);
        }
    }, [callData]);

    const generateKitToken = (roomId: string, userId: string) => {
        return ZegoUIKitPrebuilt.generateKitTokenForTest(
            ZEGO_CONFIG.appId,
            ZEGO_CONFIG.serverSecret,
            roomId,
            userId,
            `User_${userId}`
        );
    };

    const joinCall = async () => {
        if (!callData || !videoContainerRef.current) return;

        setIsJoining(true);

        try {
            // Use conversationId as temporary userId for ZegoCloud
            const tempUserId = `conv_${conversationId}_${Date.now()}`;
            const kitToken = generateKitToken(callData.roomId, tempUserId);

            const zp = ZegoUIKitPrebuilt.create(kitToken);
            zegoInstanceRef.current = zp;

            const isVideoCall = callData.roomConfig?.enableVideo !== false;

            await zp.joinRoom({
                container: videoContainerRef.current,
                scenario: {
                    mode: isVideoCall
                        ? ZegoUIKitPrebuilt.VideoConference
                        : ZegoUIKitPrebuilt.VideoConference,
                },
                maxUsers: callData.roomConfig?.maxUsers || 8,
                showPreJoinView: false,
                showRoomTimer: true,
                showUserList: true,
                showTextChat: false,
                showScreenSharingButton: callData.roomConfig?.enableScreenSharing !== false,
                showLayoutButton: true,
                showNonVideoUser: true,
                onJoinRoom: () => {
                    console.log('Joined ZegoCloud room:', callData.roomId);
                    setCallStatus(CallStatus.ACCEPTED);
                    setIsJoining(false);
                },
                onLeaveRoom: () => {
                    console.log('Left ZegoCloud room:', callData.roomId);
                    handleEndCall();
                },
                onUserJoin: (users: any[]) => {
                    console.log('Users joined:', users);
                },
                onUserLeave: (users: any[]) => {
                    console.log('Users left:', users);
                }
            });

        } catch (error) {
            console.error('Failed to join call:', error);
            setIsJoining(false);
        }
    };

    const handleAcceptCall = async () => {
        if (!callData) return;

        try {
            const response = await axios.post<VideoCallResponse>(
                `/api/video-calls/${callData.callId}/accept`
            );

            if (response.data) {
                await joinCall();
            }
        } catch (error) {
            console.error('Failed to accept call:', error);
        }
    };

    const handleRejectCall = async () => {
        if (!callData) return;

        try {
            await axios.post(
                `/api/video-calls/${callData.callId}/reject`
            );
            onClose();
        } catch (error) {
            console.error('Failed to reject call:', error);
        }
    };

    const handleEndCall = async () => {
        if (!callData) return;

        try {
            // Leave ZegoCloud room
            if (zegoInstanceRef.current) {
                zegoInstanceRef.current.destroy();
                zegoInstanceRef.current = null;
            }

            // Notify backend
            await axios.post(
                `/api/video-calls/${callData.callId}/end`
            );

            onClose();
        } catch (error) {
            console.error('Failed to end call:', error);
            onClose();
        }
    };

    if (!isOpen || !callData) return null;

    return (
        <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg shadow-xl w-full h-full max-w-6xl max-h-[90vh] overflow-hidden">
                {/* Call Header */}
                <div className="bg-gray-800 text-white p-4 flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                        {callerAvatar && (
                            <img
                                src={callerAvatar}
                                alt={callerName}
                                className="w-10 h-10 rounded-full"
                            />
                        )}
                        <div>
                            <h3 className="font-semibold">
                                {isIncoming ? `${callerName} is calling...` : 'Video Call'}
                            </h3>
                            <p className="text-sm text-gray-300">
                                {callStatus === CallStatus.INITIATED && 'Connecting...'}
                                {callStatus === CallStatus.ACCEPTED && 'Connected'}
                                {callStatus === CallStatus.ENDED && 'Call ended'}
                            </p>
                        </div>
                    </div>

                    <button
                        onClick={handleEndCall}
                        className="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg transition-colors"
                    >
                        End Call
                    </button>
                </div>

                {/* Video Container */}
                <div className="flex-1 bg-gray-900 relative">
                    {callStatus === CallStatus.INITIATED && isIncoming ? (
                        // Incoming call UI
                        <div className="flex items-center justify-center h-full">
                            <div className="text-center text-white">
                                <div className="mb-6">
                                    {callerAvatar ? (
                                        <img
                                            src={callerAvatar}
                                            alt={callerName}
                                            className="w-24 h-24 rounded-full mx-auto mb-4"
                                        />
                                    ) : (
                                        <div className="w-24 h-24 bg-gray-600 rounded-full mx-auto mb-4 flex items-center justify-center">
                                            <span className="text-2xl">👤</span>
                                        </div>
                                    )}
                                    <h2 className="text-2xl font-semibold mb-2">{callerName}</h2>
                                    <p className="text-gray-300">Incoming video call...</p>
                                </div>

                                <div className="flex space-x-4 justify-center">
                                    <button
                                        onClick={handleRejectCall}
                                        className="bg-red-500 hover:bg-red-600 text-white p-4 rounded-full transition-colors"
                                    >
                                        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                        </svg>
                                    </button>

                                    <button
                                        onClick={handleAcceptCall}
                                        disabled={isJoining}
                                        className="bg-green-500 hover:bg-green-600 text-white p-4 rounded-full transition-colors disabled:opacity-50"
                                    >
                                        {isJoining ? (
                                            <div className="w-6 h-6 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                                        ) : (
                                            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                                            </svg>
                                        )}
                                    </button>
                                </div>
                            </div>
                        </div>
                    ) : (
                        // Video call container
                        <div
                            ref={videoContainerRef}
                            className="w-full h-full"
                            style={{ minHeight: '500px' }}
                        />
                    )}
                </div>
            </div>
        </div>
    );
};

export default VideoCallModal;
