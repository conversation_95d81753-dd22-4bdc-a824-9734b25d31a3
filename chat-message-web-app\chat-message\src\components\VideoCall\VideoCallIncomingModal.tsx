'use client';

import React, { useEffect, useState } from 'react';
import { VideoCallInviteMessage, CallStatus } from '@/types/videoCall';
import VideoCallModal from './VideoCallModal';

interface VideoCallIncomingModalProps {
    incomingCall: VideoCallInviteMessage | null;
    onAccept: () => void;
    onReject: () => void;
}

const VideoCallIncomingModal: React.FC<VideoCallIncomingModalProps> = ({
    incomingCall,
    onAccept,
    onReject
}) => {
    const [showVideoCall, setShowVideoCall] = useState(false);

    const handleAccept = () => {
        setShowVideoCall(true);
        onAccept();
    };

    const handleReject = () => {
        onReject();
    };

    const handleCloseVideoCall = () => {
        setShowVideoCall(false);
    };

    if (!incomingCall) return null;

    if (showVideoCall) {
        return (
            <VideoCallModal
                isOpen={true}
                onClose={handleCloseVideoCall}
                callData={{
                    callId: incomingCall.callId,
                    roomId: incomingCall.roomId,
                    token: '',
                    status: CallStatus.ACCEPTED,
                    roomConfig: incomingCall.roomConfig
                }}
                conversationId={incomingCall.conversationId}
                isIncoming={true}
                callerName={incomingCall.callerName}
                callerAvatar={incomingCall.callerAvatar}
            />
        );
    }

    return (
        <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg shadow-xl p-8 max-w-md w-full mx-4">
                <div className="text-center">
                    {/* Caller Avatar */}
                    <div className="mb-6">
                        {incomingCall.callerAvatar ? (
                            <img 
                                src={incomingCall.callerAvatar} 
                                alt={incomingCall.callerName}
                                className="w-20 h-20 rounded-full mx-auto mb-4"
                            />
                        ) : (
                            <div className="w-20 h-20 bg-gray-300 rounded-full mx-auto mb-4 flex items-center justify-center">
                                <span className="text-2xl text-gray-600">👤</span>
                            </div>
                        )}
                        <h2 className="text-xl font-semibold text-gray-900 mb-2">
                            {incomingCall.callerName}
                        </h2>
                        <p className="text-gray-600">
                            Incoming {incomingCall.callType.toLowerCase()} call...
                        </p>
                    </div>

                    {/* Call Actions */}
                    <div className="flex justify-center space-x-6">
                        {/* Reject Button */}
                        <button
                            onClick={handleReject}
                            className="bg-red-500 hover:bg-red-600 text-white p-4 rounded-full transition-colors shadow-lg"
                            title="Reject call"
                        >
                            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </button>

                        {/* Accept Button */}
                        <button
                            onClick={handleAccept}
                            className="bg-green-500 hover:bg-green-600 text-white p-4 rounded-full transition-colors shadow-lg"
                            title="Accept call"
                        >
                            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default VideoCallIncomingModal;
