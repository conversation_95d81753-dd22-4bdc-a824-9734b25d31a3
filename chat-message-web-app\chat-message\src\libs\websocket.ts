import { Client, IMessage, StompSubscription } from "@stomp/stompjs";
import SockJ<PERSON> from "sockjs-client";
import { ChatRequest, ChatMessage, ConnectionStatus, ConversationCreationResponse } from "@/types/chat";
import { VideoCallInviteMessage, VideoCallAcceptedMessage, VideoCallRejectedMessage, VideoCallEndedMessage } from "@/types/videoCall";

export interface ConversationUpdateEvent {
    type: 'CONVERSATION_CREATED' | 'CONVERSATION_UPDATED' | 'CONVERSATION_DELETED';
    conversation: ConversationCreationResponse;
    userId?: string; // Who triggered the action (from backend)
    timestamp: number;
}

const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || "http://localhost:8080";
const SOCKET_URL = `${API_BASE_URL}/ws`;

const CONNECTION_CONFIG = {
    heartbeatIncoming: 10000,
    heartbeatOutgoing: 10000,
    reconnectDelay: 1000, // Start with 1s - <PERSON>n định hơn
    maxReconnectDelay: 10000, // Max 10 seconds - <PERSON>n định
    maxReconnectAttempts: 10, // Ít attempts hơn để tránh spam
    connectionTimeout: 10000, // 10 seconds timeout - đủ thời gian
    debug: process.env.NODE_ENV === 'development'
};

export class WebSocketService {
    private static instance: WebSocketService;
    private stompClient: Client | null = null;
    private messageSubscription: StompSubscription | null = null;
    private conversationSubscription: StompSubscription | null = null;
    private videoCallSubscription: StompSubscription | null = null;

    private connectionStatus: ConnectionStatus = {
        isConnected: false,
        reconnectAttempts: 0
    };

    private onMessageCallback?: (message: ChatMessage) => void;
    private onStatusChangeCallback?: (status: ConnectionStatus) => void;
    private onConversationUpdateCallback?: (event: ConversationUpdateEvent) => void;
    private onVideoCallInviteCallback?: (invite: VideoCallInviteMessage) => void;
    private onVideoCallAcceptedCallback?: (accepted: VideoCallAcceptedMessage) => void;
    private onVideoCallRejectedCallback?: (rejected: VideoCallRejectedMessage) => void;
    private onVideoCallEndedCallback?: (ended: VideoCallEndedMessage) => void;

    private reconnectTimer?: NodeJS.Timeout;
    private connectionTimer?: NodeJS.Timeout;
    private isConnecting = false;
    private shouldReconnect = true;

    static getInstance(): WebSocketService {
        if (!WebSocketService.instance) {
            WebSocketService.instance = new WebSocketService();
        }
        return WebSocketService.instance;
    }

    connect(
        onMessage: (message: ChatMessage) => void,
        onStatusChange?: (status: ConnectionStatus) => void,
        onConversationUpdate?: (event: ConversationUpdateEvent) => void,
        onVideoCallInvite?: (invite: VideoCallInviteMessage) => void,
        onVideoCallAccepted?: (accepted: VideoCallAcceptedMessage) => void,
        onVideoCallRejected?: (rejected: VideoCallRejectedMessage) => void,
        onVideoCallEnded?: (ended: VideoCallEndedMessage) => void
    ): void {
        if (this.stompClient && this.stompClient.connected) {
            return;
        }

        if (this.isConnecting) {
            return;
        }

        this.onMessageCallback = onMessage;
        this.onStatusChangeCallback = onStatusChange;
        this.onConversationUpdateCallback = onConversationUpdate;
        this.onVideoCallInviteCallback = onVideoCallInvite;
        this.onVideoCallAcceptedCallback = onVideoCallAccepted;
        this.onVideoCallRejectedCallback = onVideoCallRejected;
        this.onVideoCallEndedCallback = onVideoCallEnded;
        this.shouldReconnect = true;

        // Reset connection status
        this.connectionStatus = { isConnected: false, reconnectAttempts: 0 };
        this.onStatusChangeCallback?.(this.connectionStatus);

        this.createConnection();
    }

    private clearTimers(): void {
        if (this.reconnectTimer) {
            clearTimeout(this.reconnectTimer);
            this.reconnectTimer = undefined;
        }
        if (this.connectionTimer) {
            clearTimeout(this.connectionTimer);
            this.connectionTimer = undefined;
        }
    }

    private cleanupConnection(): void {
        if (this.stompClient) {
            try {
                this.messageSubscription?.unsubscribe();
                this.conversationSubscription?.unsubscribe();
                this.videoCallSubscription?.unsubscribe();
                this.messageSubscription = null;
                this.conversationSubscription = null;
                this.videoCallSubscription = null;
                this.stompClient.deactivate();
            } catch (error) {
                console.warn('⚠️ Error cleaning up existing connection:', error);
            }
        }
    }

    private createConnection(): void {
        if (this.isConnecting) {
            return;
        }

        const accessToken = localStorage.getItem("accessToken");
        if (!accessToken) {
            this.connectionStatus = { isConnected: false, reconnectAttempts: 0 };
            this.onStatusChangeCallback?.(this.connectionStatus);
            return;
        }

        this.isConnecting = true;

        // Clear any existing timers
        this.clearTimers();


        // Cleanup existing connection
        this.cleanupConnection();

        // Connection timeout - CHỈ timeout nếu thực sự không connect được
        this.connectionTimer = setTimeout(() => {
            if (!this.stompClient?.connected) {
                this.handleConnectionError();
            }
        }, CONNECTION_CONFIG.connectionTimeout);

        this.stompClient = new Client({
            webSocketFactory: () => new SockJS(SOCKET_URL),
            connectHeaders: {
                Authorization: `Bearer ${accessToken}`,
            },
            heartbeatIncoming: CONNECTION_CONFIG.heartbeatIncoming,
            heartbeatOutgoing: CONNECTION_CONFIG.heartbeatOutgoing,
            debug: CONNECTION_CONFIG.debug ? console.log : undefined,
            onConnect: () => {
                this.clearTimers();
                this.isConnecting = false;
                this.connectionStatus = { isConnected: true, reconnectAttempts: 0 };
                this.onStatusChangeCallback?.(this.connectionStatus);

                this.messageSubscription = this.stompClient?.subscribe("/user/queue/messages", (message: IMessage) => {
                    try {
                        const chatMessage: ChatMessage = JSON.parse(message.body);
                        this.onMessageCallback?.(chatMessage);
                    } catch (error) {
                    }
                }) || null;

                this.conversationSubscription = this.stompClient?.subscribe("/user/queue/conversation-updates", (message: IMessage) => {
                    try {
                        if (CONNECTION_CONFIG.debug) {
                            console.log('📨 Received conversation update:', message.body);
                        }
                        const conversationEvent: ConversationUpdateEvent = JSON.parse(message.body);
                        this.onConversationUpdateCallback?.(conversationEvent);
                    } catch (error) {
                        if (CONNECTION_CONFIG.debug) {
                            console.error('❌ Failed to parse conversation update:', error);
                        }
                    }
                }) || null;

                // Subscribe to video call events
                this.videoCallSubscription = this.stompClient?.subscribe("/user/queue/video-calls", (message: IMessage) => {
                    try {
                        if (CONNECTION_CONFIG.debug) {
                            console.log('📞 Received video call event:', message.body);
                        }
                        const callEvent = JSON.parse(message.body);

                        // Handle different types of video call events
                        if (callEvent.status === 'INITIATED') {
                            this.onVideoCallInviteCallback?.(callEvent as VideoCallInviteMessage);
                        } else if (callEvent.status === 'ACCEPTED') {
                            this.onVideoCallAcceptedCallback?.(callEvent as VideoCallAcceptedMessage);
                        } else if (callEvent.status === 'REJECTED') {
                            this.onVideoCallRejectedCallback?.(callEvent as VideoCallRejectedMessage);
                        } else if (callEvent.status === 'ENDED') {
                            this.onVideoCallEndedCallback?.(callEvent as VideoCallEndedMessage);
                        }
                    } catch (error) {
                        console.error('❌ Failed to parse video call event:', error);
                    }
                }) || null;

            },
            onStompError: (frame) => {
                console.error('🔴 WebSocket STOMP error:', frame);
                this.handleConnectionError();
            },
            onWebSocketClose: (event) => {
                console.warn('🟡 WebSocket connection closed:', {
                    code: event.code,
                    reason: event.reason,
                    wasClean: event.wasClean
                });
                this.isConnecting = false;
                this.connectionStatus.isConnected = false;
                this.onStatusChangeCallback?.(this.connectionStatus);

                // Only auto-reconnect if it wasn't a clean close
                if (!event.wasClean && this.shouldReconnect) {
                    this.handleConnectionError();
                }
            },
            onWebSocketError: (event) => {
                console.error('🔴 WebSocket error:', event);
                this.isConnecting = false;
                this.handleConnectionError();
            },
            reconnectDelay: 0, // Disable auto-reconnect, we handle it manually
        });

        try {
            this.stompClient.activate();
        } catch (error) {
            console.error('❌ Failed to activate WebSocket:', error);
            this.handleConnectionError();
        }
    }

    private handleConnectionError(): void {
        this.isConnecting = false;
        this.clearTimers();
        this.connectionStatus.isConnected = false;
        this.onStatusChangeCallback?.(this.connectionStatus);

        if (!this.shouldReconnect) return;

        // Auto-reconnect if under max attempts
        if (this.connectionStatus.reconnectAttempts < CONNECTION_CONFIG.maxReconnectAttempts) {
            this.connectionStatus.reconnectAttempts++;

            const baseDelay = Math.min(
                CONNECTION_CONFIG.reconnectDelay * Math.pow(2, this.connectionStatus.reconnectAttempts - 1),
                CONNECTION_CONFIG.maxReconnectDelay
            );
            const jitter = Math.random() * 1000; // Add up to 1 second jitter
            const backoffDelay = baseDelay + jitter;

            this.reconnectTimer = setTimeout(() => {
                if (this.shouldReconnect) {
                    this.createConnection();
                }
            }, backoffDelay);
        } else {
            console.error('❌ Max reconnection attempts reached');
            this.shouldReconnect = false;
        }
    }

    disconnect(): void {
        this.shouldReconnect = false;
        this.clearTimers();
        this.cleanupConnection();
        this.connectionStatus = { isConnected: false, reconnectAttempts: 0 };
        this.onStatusChangeCallback?.(this.connectionStatus);
        console.log('🔌 WebSocket disconnected');
    }

    sendMessage(request: ChatRequest): boolean {
        if (!this.stompClient || !this.stompClient.connected) {
            console.warn('⚠️ Cannot send message: WebSocket not connected');
            return false;
        }

        try {
            this.stompClient.publish({
                destination: "/app/chat",
                body: JSON.stringify(request),
            });
            return true;
        } catch (error) {
            console.error('❌ Failed to send message:', error);
            return false;
        }
    }

    markAsRead(conversationId: string, messageId: string): boolean {
        if (!this.stompClient?.connected) {
            console.warn('⚠️ Cannot mark as read: WebSocket not connected');
            return false;
        }

        // Check if user is authenticated
        const accessToken = localStorage.getItem("accessToken");
        if (!accessToken) {
            console.warn('⚠️ Cannot mark as read: No access token');
            return false;
        }

        try {
            this.stompClient.publish({
                destination: `/app/chat/${conversationId}/read/${messageId}`,
                body: '',
            });
            return true;
        } catch (error) {
            console.error('❌ Failed to mark message as read:', error);
            return false;
        }
    }

    getConnectionStatus(): ConnectionStatus {
        return { ...this.connectionStatus };
    }

    isConnected(): boolean {
        return this.connectionStatus.isConnected && this.stompClient?.connected === true;
    }

    // Manual reconnect method
    reconnect(): void {
        this.shouldReconnect = true;
        this.connectionStatus.reconnectAttempts = 0; // Reset attempts
        this.disconnect();
        setTimeout(() => {
            this.createConnection();
        }, 500);
    }

    forceReconnect(): void {
        this.shouldReconnect = true;
        this.connectionStatus.reconnectAttempts = 0;
        this.clearTimers();
        this.cleanupConnection();

        setTimeout(() => {
            this.createConnection();
        }, 100);
    }
}

// Export singleton instance for production use
export const webSocketService = WebSocketService.getInstance();

// Export for testing and configuration
export { CONNECTION_CONFIG };
