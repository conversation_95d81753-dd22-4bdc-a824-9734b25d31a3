{"version": 3, "sources": [], "sections": [{"offset": {"line": 135, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Chat-Service/chat-message-web-app/chat-message/src/types/chat.ts"], "sourcesContent": ["export interface ParticipantInfoDetailResponse {\r\n    userId: string;\r\n    username: string;\r\n    avatar: string | null;\r\n}\r\n\r\nexport enum ConversationType {\r\n    PRIVATE = 'PRIVATE',\r\n    GROUP = 'GROUP'\r\n}\r\n\r\nexport interface ConversationCreationRequest {\r\n    participantIds: string[];\r\n    conversationType: ConversationType;\r\n    conversationName?: string;\r\n    conversationAvatar?: string;\r\n}\r\n\r\nexport interface ConversationCreationResponse {\r\n    id: string;\r\n    conversationType: \"PRIVATE\" | \"GROUP\";\r\n    participantHash: string;\r\n    conversationAvatar: string | null;\r\n    conversationName: string;\r\n    participantInfo: ParticipantInfoDetailResponse[];\r\n    createdAt: string;\r\n}\r\n\r\nexport interface ChatMessage {\r\n    id: string;\r\n    tempId?: string;\r\n    conversationId: string;\r\n    me: boolean;\r\n    username?: string; // Backend field for sender username\r\n    content: string;\r\n    status: \"SENDING\" | \"SENT\";\r\n    createdAt: string;\r\n    read?: boolean; // Backend uses 'read' not 'isRead'\r\n    mediaUrl?: string[]; // Keep for backward compatibility\r\n    mediaAttachments?: MediaAttachment[]; // New field from backend\r\n    messageType?: \"TEXT\" | \"FILE\";\r\n}\r\n\r\nimport { MediaAttachment, MessageType } from './file';\r\n\r\nexport interface ChatRequest {\r\n    conversationId: string;\r\n    sender?: string;\r\n    content: string;\r\n    tempId?: string; // Optional - BE will generate if not provided\r\n    mediaAttachments?: MediaAttachment[];\r\n    messageType: MessageType;\r\n}\r\n\r\nexport interface ApiResponse<T> {\r\n    code: number;\r\n    data: T;\r\n    message?: string;\r\n}\r\n\r\nexport interface PageResponse<T> {\r\n    currentPages: number;\r\n    pageSizes: number;\r\n    totalPages: number;\r\n    totalElements: number;\r\n    data: T[];\r\n}\r\n\r\nexport interface ConnectionStatus {\r\n    isConnected: boolean;\r\n    reconnectAttempts: number;\r\n}\r\n"], "names": [], "mappings": ";;;AAMO,IAAA,AAAK,0CAAA;;;WAAA", "debugId": null}}, {"offset": {"line": 149, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Chat-Service/chat-message-web-app/chat-message/src/api/axios.ts"], "sourcesContent": ["import axios, { AxiosResponse, AxiosError, AxiosProgressEvent } from \"axios\";\r\nimport { ApiResponse, ConversationCreationResponse, ChatMessage, ParticipantInfoDetailResponse, PageResponse, ConversationType } from \"@/types/chat\";\r\nimport { FileMetaDataResponse, UploadProgress } from \"@/types/file\";\r\n\r\n\r\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || \"http://localhost:8080\";\r\n\r\nconst apiClient = axios.create({\r\n    baseURL: API_BASE_URL,\r\n    timeout: 10000,\r\n    headers: {\r\n        'Content-Type': 'application/json',\r\n    },\r\n});\r\n\r\napiClient.interceptors.request.use(\r\n    (config) => {\r\n        const token = localStorage.getItem(\"accessToken\");\r\n        if (token) {\r\n            config.headers[\"Authorization\"] = `Bearer ${token}`;\r\n        }\r\n        return config;\r\n    },\r\n    (error: AxiosError) => {\r\n        return Promise.reject(error);\r\n    }\r\n);\r\n\r\napiClient.interceptors.response.use(\r\n    (response: AxiosResponse) => response,\r\n    (error: AxiosError) => {\r\n        if (error.response?.status === 401) {\r\n            localStorage.removeItem(\"accessToken\");\r\n            window.location.href = \"/auth/login\";\r\n        }\r\n        return Promise.reject(error);\r\n    }\r\n);\r\n\r\nexport class ApiService {\r\n    static async getConversations(): Promise<ConversationCreationResponse[]> {\r\n        try {\r\n            const response = await apiClient.get<ApiResponse<ConversationCreationResponse[]>>(\"/conversations\");\r\n            return response.data.data || [];\r\n        } catch (error) {\r\n            throw error;\r\n        }\r\n    }\r\n\r\n\r\n\r\n    static async getMessages(conversationId: string, page: number = 1, size: number = 15): Promise<PageResponse<ChatMessage>> {\r\n        try {\r\n            const response = await apiClient.get<ApiResponse<PageResponse<ChatMessage>>>(\r\n                `/chat/${conversationId}?page=${page}&size=${size}`\r\n            );\r\n\r\n            const pageData = response.data.data;\r\n\r\n            if (!pageData) {\r\n                return {\r\n                    currentPages: 1,\r\n                    pageSizes: size,\r\n                    totalPages: 0,\r\n                    totalElements: 0,\r\n                    data: []\r\n                };\r\n            }\r\n\r\n            return pageData;\r\n        } catch (error) {\r\n            throw error;\r\n        }\r\n    }\r\n\r\n    static async searchUsers(username: string): Promise<ParticipantInfoDetailResponse[]> {\r\n        try {\r\n            const response = await apiClient.get<ApiResponse<ParticipantInfoDetailResponse[]>>(\r\n                `/users?username=${encodeURIComponent(username)}`\r\n            );\r\n            return response.data.data || [];\r\n        } catch (error) {\r\n            throw error;\r\n        }\r\n    }\r\n\r\n    static async createConversation(\r\n        participantIds: string[],\r\n        conversationType: ConversationType = ConversationType.PRIVATE,\r\n        conversationName?: string,\r\n        conversationAvatar?: string\r\n    ): Promise<ConversationCreationResponse> {\r\n        try {\r\n            const response = await apiClient.post<ApiResponse<ConversationCreationResponse>>(\"/conversations\", {\r\n                participantIds,\r\n                conversationType,\r\n                conversationName,\r\n                conversationAvatar\r\n            });\r\n            return response.data.data;\r\n        } catch (error) {\r\n            throw error;\r\n        }\r\n    }\r\n\r\n    static async deleteConversation(conversationId: string): Promise<void> {\r\n        try {\r\n            await apiClient.delete<ApiResponse<void>>(`/conversations/${conversationId}`);\r\n        } catch (error) {\r\n            throw error;\r\n        }\r\n    }\r\n\r\n    static async updateFcmToken(fcmToken: string): Promise<void> {\r\n        try {\r\n            await apiClient.post(\"/users/register-fcm-token\", { fcmToken });\r\n        } catch (error) {\r\n            throw error;\r\n        }\r\n    }\r\n\r\n    static async markAsRead(conversationId: string, messageId: string): Promise<void> {\r\n        try {\r\n            await apiClient.post(`/chat/${conversationId}/read/${messageId}`);\r\n        } catch (error) {\r\n            console.error('❌ Failed to mark as read via HTTP:', error);\r\n            throw error;\r\n        }\r\n    }\r\n\r\n    static async login(email: string, password: string): Promise<{ accessToken: string; refreshToken: string }> {\r\n        try {\r\n            const response = await apiClient.post(\"/auth/sign-in\", {\r\n                email,\r\n                password\r\n            });\r\n            return response.data.data;\r\n        } catch (error) {\r\n            throw error;\r\n        }\r\n    }\r\n\r\n    static async register(email: string, username: string, password: string): Promise<void> {\r\n        try {\r\n            await apiClient.post(\"/users\", {\r\n                email,\r\n                username,\r\n                password\r\n            });\r\n        } catch (error) {\r\n            throw error;\r\n        }\r\n    }\r\n\r\n    static async uploadFilesSync(\r\n        files: File[],\r\n        onProgress?: (progressEvent: AxiosProgressEvent) => void\r\n    ): Promise<FileMetaDataResponse[]> {\r\n        const formData = new FormData();\r\n\r\n        files.forEach((file) => {\r\n            formData.append('files', file);\r\n        });\r\n\r\n        try {\r\n            const response = await apiClient.post<ApiResponse<FileMetaDataResponse[]>>(\r\n                '/files/upload-media-sync',\r\n                formData,\r\n                {\r\n                    headers: {\r\n                        'Content-Type': 'multipart/form-data',\r\n                    },\r\n                    onUploadProgress: onProgress\r\n                }\r\n            );\r\n\r\n            return response.data.data || [];\r\n        } catch (error) {\r\n            throw error;\r\n        }\r\n    }\r\n\r\n    static async uploadFilesSyncWithProgress(\r\n        files: File[],\r\n        onProgress?: (progress: UploadProgress[]) => void\r\n    ): Promise<FileMetaDataResponse[]> {\r\n        try {\r\n            const result = await this.uploadFilesSync(files, (progressEvent) => {\r\n                if (onProgress && progressEvent.total) {\r\n                    const progress = Math.round(\r\n                        (progressEvent.loaded * 100) / progressEvent.total\r\n                    );\r\n\r\n                    // Create progress for all files (simplified for sync upload)\r\n                    const progressArray: UploadProgress[] = files.map((file, index) => ({\r\n                        fileId: `${file.name}-${index}`,\r\n                        progress: progress,\r\n                        status: progress === 100 ? 'completed' : 'uploading'\r\n                    }));\r\n\r\n                    onProgress(progressArray);\r\n                }\r\n            });\r\n\r\n            return result;\r\n        } catch (error) {\r\n            console.error('File upload error:', error);\r\n            throw new Error('Upload failed');\r\n        }\r\n    }\r\n\r\n    static async uploadFilesAsync(\r\n        files: File[],\r\n        onProgress?: (progressEvent: AxiosProgressEvent) => void\r\n    ): Promise<FileMetaDataResponse[]> {\r\n        const formData = new FormData();\r\n\r\n        files.forEach((file) => {\r\n            formData.append('files', file);\r\n        });\r\n\r\n        try {\r\n            const response = await apiClient.post<ApiResponse<FileMetaDataResponse[]>>(\r\n                '/files/upload-media-async',\r\n                formData,\r\n                {\r\n                    headers: {\r\n                        'Content-Type': 'multipart/form-data',\r\n                    },\r\n                    onUploadProgress: onProgress\r\n                }\r\n            );\r\n\r\n            return response.data.data || [];\r\n        } catch (error) {\r\n            console.error('Error uploading files async:', error);\r\n            throw error;\r\n        }\r\n    }\r\n\r\n    static async uploadFilesAsyncWithProgress(\r\n        files: File[],\r\n        onProgress?: (progress: UploadProgress[]) => void\r\n    ): Promise<FileMetaDataResponse[]> {\r\n        try {\r\n            const result = await this.uploadFilesAsync(files, (progressEvent) => {\r\n                if (onProgress && progressEvent.total) {\r\n                    const progress = Math.round(\r\n                        (progressEvent.loaded * 100) / progressEvent.total\r\n                    );\r\n\r\n                    const progressArray: UploadProgress[] = files.map((file, index) => ({\r\n                        fileId: `${file.name}-${index}`,\r\n                        progress: progress,\r\n                        status: progress === 100 ? 'completed' : 'uploading'\r\n                    }));\r\n\r\n                    onProgress(progressArray);\r\n                }\r\n            });\r\n\r\n            return result;\r\n        } catch (error) {\r\n            console.error('File upload error:', error);\r\n\r\n            // Preserve original error message\r\n            if (error instanceof Error) {\r\n                throw error;\r\n            }\r\n\r\n            // For axios errors, extract meaningful message\r\n            if (error && typeof error === 'object' && 'response' in error) {\r\n                const axiosError = error as any;\r\n                const message = axiosError.response?.data?.message ||\r\n                    axiosError.response?.data?.error ||\r\n                    axiosError.message ||\r\n                    'Upload failed';\r\n                throw new Error(message);\r\n            }\r\n\r\n            throw new Error('Upload failed');\r\n        }\r\n    }\r\n\r\n    static validateFile(file: File): { isValid: boolean; error?: string } {\r\n        const maxSize = 100 * 1024 * 1024; // 100MB\r\n\r\n        if (file.size > maxSize) {\r\n            return {\r\n                isValid: false,\r\n                error: `File \"${file.name}\" exceeds the 100MB size limit (${this.formatFileSize(file.size)}). Please choose a smaller file.`\r\n            };\r\n        }\r\n\r\n        const allowedTypes = [\r\n            // Images\r\n            'image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml', 'image/bmp', 'image/tiff',\r\n            // Videos\r\n            'video/mp4', 'video/webm', 'video/ogg', 'video/avi', 'video/mov', 'video/wmv', 'video/flv', 'video/mkv',\r\n            // Audio\r\n            'audio/mp3', 'audio/wav', 'audio/ogg', 'audio/aac', 'audio/flac', 'audio/m4a', 'audio/wma',\r\n            // Documents\r\n            'application/pdf', 'application/msword',\r\n            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',\r\n            'application/rtf', 'application/vnd.oasis.opendocument.text',\r\n            // Spreadsheets\r\n            'application/vnd.ms-excel',\r\n            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',\r\n            'application/vnd.oasis.opendocument.spreadsheet',\r\n            // Presentations\r\n            'application/vnd.ms-powerpoint',\r\n            'application/vnd.openxmlformats-officedocument.presentationml.presentation',\r\n            'application/vnd.oasis.opendocument.presentation',\r\n            // Archives\r\n            'application/zip', 'application/x-rar-compressed', 'application/x-7z-compressed', 'application/x-tar', 'application/gzip',\r\n            // Text\r\n            'text/plain', 'text/csv', 'text/html', 'text/css', 'text/javascript', 'text/xml', 'application/json', 'text/markdown'\r\n        ];\r\n\r\n        if (!allowedTypes.includes(file.type)) {\r\n            const error = `🚫 File \"${file.name}\" format is not supported. Please choose a different file type.`;\r\n            return {\r\n                isValid: false,\r\n                error\r\n            };\r\n        }\r\n\r\n        return { isValid: true };\r\n    }\r\n\r\n    static validateFiles(files: File[]): { isValid: boolean; errors: string[] } {\r\n        const maxFiles = 10;\r\n        const errors: string[] = [];\r\n\r\n        if (files.length > maxFiles) {\r\n            errors.push(`📂 Maximum ${maxFiles} files allowed per message. Please select fewer files.`);\r\n        }\r\n\r\n        files.forEach((file, index) => {\r\n            const validation = this.validateFile(file);\r\n            if (!validation.isValid) {\r\n                errors.push(validation.error || `❌ File ${index + 1} (${file.name}): Invalid file`);\r\n            }\r\n        });\r\n\r\n        return {\r\n            isValid: errors.length === 0,\r\n            errors\r\n        };\r\n    }\r\n\r\n    static formatFileSize(bytes: number): string {\r\n        if (bytes === 0) return '0 Bytes';\r\n\r\n        const k = 1024;\r\n        const sizes = ['Bytes', 'KB', 'MB', 'GB'];\r\n        const i = Math.floor(Math.log(bytes) / Math.log(k));\r\n\r\n        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\r\n    }\r\n\r\n    static createImagePreview(file: File): Promise<string> {\r\n        return new Promise((resolve, reject) => {\r\n            if (!file.type.startsWith('image/')) {\r\n                reject(new Error('File is not an image'));\r\n                return;\r\n            }\r\n\r\n            const reader = new FileReader();\r\n            reader.onload = (e) => {\r\n                resolve(e.target?.result as string);\r\n            };\r\n            reader.onerror = () => {\r\n                reject(new Error('Failed to read file'));\r\n            };\r\n            reader.readAsDataURL(file);\r\n        });\r\n    }\r\n}\r\n\r\nexport default apiClient;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAIA,MAAM,eAAe,6DAAwC;AAE7D,MAAM,YAAY,qIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IAC3B,SAAS;IACT,SAAS;IACT,SAAS;QACL,gBAAgB;IACpB;AACJ;AAEA,UAAU,YAAY,CAAC,OAAO,CAAC,GAAG,CAC9B,CAAC;IACG,MAAM,QAAQ,aAAa,OAAO,CAAC;IACnC,IAAI,OAAO;QACP,OAAO,OAAO,CAAC,gBAAgB,GAAG,CAAC,OAAO,EAAE,OAAO;IACvD;IACA,OAAO;AACX,GACA,CAAC;IACG,OAAO,QAAQ,MAAM,CAAC;AAC1B;AAGJ,UAAU,YAAY,CAAC,QAAQ,CAAC,GAAG,CAC/B,CAAC,WAA4B,UAC7B,CAAC;IACG,IAAI,MAAM,QAAQ,EAAE,WAAW,KAAK;QAChC,aAAa,UAAU,CAAC;QACxB,OAAO,QAAQ,CAAC,IAAI,GAAG;IAC3B;IACA,OAAO,QAAQ,MAAM,CAAC;AAC1B;AAGG,MAAM;IACT,aAAa,mBAA4D;QACrE,IAAI;YACA,MAAM,WAAW,MAAM,UAAU,GAAG,CAA8C;YAClF,OAAO,SAAS,IAAI,CAAC,IAAI,IAAI,EAAE;QACnC,EAAE,OAAO,OAAO;YACZ,MAAM;QACV;IACJ;IAIA,aAAa,YAAY,cAAsB,EAAE,OAAe,CAAC,EAAE,OAAe,EAAE,EAAsC;QACtH,IAAI;YACA,MAAM,WAAW,MAAM,UAAU,GAAG,CAChC,CAAC,MAAM,EAAE,eAAe,MAAM,EAAE,KAAK,MAAM,EAAE,MAAM;YAGvD,MAAM,WAAW,SAAS,IAAI,CAAC,IAAI;YAEnC,IAAI,CAAC,UAAU;gBACX,OAAO;oBACH,cAAc;oBACd,WAAW;oBACX,YAAY;oBACZ,eAAe;oBACf,MAAM,EAAE;gBACZ;YACJ;YAEA,OAAO;QACX,EAAE,OAAO,OAAO;YACZ,MAAM;QACV;IACJ;IAEA,aAAa,YAAY,QAAgB,EAA4C;QACjF,IAAI;YACA,MAAM,WAAW,MAAM,UAAU,GAAG,CAChC,CAAC,gBAAgB,EAAE,mBAAmB,WAAW;YAErD,OAAO,SAAS,IAAI,CAAC,IAAI,IAAI,EAAE;QACnC,EAAE,OAAO,OAAO;YACZ,MAAM;QACV;IACJ;IAEA,aAAa,mBACT,cAAwB,EACxB,mBAAqC,oHAAA,CAAA,mBAAgB,CAAC,OAAO,EAC7D,gBAAyB,EACzB,kBAA2B,EACU;QACrC,IAAI;YACA,MAAM,WAAW,MAAM,UAAU,IAAI,CAA4C,kBAAkB;gBAC/F;gBACA;gBACA;gBACA;YACJ;YACA,OAAO,SAAS,IAAI,CAAC,IAAI;QAC7B,EAAE,OAAO,OAAO;YACZ,MAAM;QACV;IACJ;IAEA,aAAa,mBAAmB,cAAsB,EAAiB;QACnE,IAAI;YACA,MAAM,UAAU,MAAM,CAAoB,CAAC,eAAe,EAAE,gBAAgB;QAChF,EAAE,OAAO,OAAO;YACZ,MAAM;QACV;IACJ;IAEA,aAAa,eAAe,QAAgB,EAAiB;QACzD,IAAI;YACA,MAAM,UAAU,IAAI,CAAC,6BAA6B;gBAAE;YAAS;QACjE,EAAE,OAAO,OAAO;YACZ,MAAM;QACV;IACJ;IAEA,aAAa,WAAW,cAAsB,EAAE,SAAiB,EAAiB;QAC9E,IAAI;YACA,MAAM,UAAU,IAAI,CAAC,CAAC,MAAM,EAAE,eAAe,MAAM,EAAE,WAAW;QACpE,EAAE,OAAO,OAAO;YACZ,QAAQ,KAAK,CAAC,sCAAsC;YACpD,MAAM;QACV;IACJ;IAEA,aAAa,MAAM,KAAa,EAAE,QAAgB,EAA0D;QACxG,IAAI;YACA,MAAM,WAAW,MAAM,UAAU,IAAI,CAAC,iBAAiB;gBACnD;gBACA;YACJ;YACA,OAAO,SAAS,IAAI,CAAC,IAAI;QAC7B,EAAE,OAAO,OAAO;YACZ,MAAM;QACV;IACJ;IAEA,aAAa,SAAS,KAAa,EAAE,QAAgB,EAAE,QAAgB,EAAiB;QACpF,IAAI;YACA,MAAM,UAAU,IAAI,CAAC,UAAU;gBAC3B;gBACA;gBACA;YACJ;QACJ,EAAE,OAAO,OAAO;YACZ,MAAM;QACV;IACJ;IAEA,aAAa,gBACT,KAAa,EACb,UAAwD,EACzB;QAC/B,MAAM,WAAW,IAAI;QAErB,MAAM,OAAO,CAAC,CAAC;YACX,SAAS,MAAM,CAAC,SAAS;QAC7B;QAEA,IAAI;YACA,MAAM,WAAW,MAAM,UAAU,IAAI,CACjC,4BACA,UACA;gBACI,SAAS;oBACL,gBAAgB;gBACpB;gBACA,kBAAkB;YACtB;YAGJ,OAAO,SAAS,IAAI,CAAC,IAAI,IAAI,EAAE;QACnC,EAAE,OAAO,OAAO;YACZ,MAAM;QACV;IACJ;IAEA,aAAa,4BACT,KAAa,EACb,UAAiD,EAClB;QAC/B,IAAI;YACA,MAAM,SAAS,MAAM,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC;gBAC9C,IAAI,cAAc,cAAc,KAAK,EAAE;oBACnC,MAAM,WAAW,KAAK,KAAK,CACvB,AAAC,cAAc,MAAM,GAAG,MAAO,cAAc,KAAK;oBAGtD,6DAA6D;oBAC7D,MAAM,gBAAkC,MAAM,GAAG,CAAC,CAAC,MAAM,QAAU,CAAC;4BAChE,QAAQ,GAAG,KAAK,IAAI,CAAC,CAAC,EAAE,OAAO;4BAC/B,UAAU;4BACV,QAAQ,aAAa,MAAM,cAAc;wBAC7C,CAAC;oBAED,WAAW;gBACf;YACJ;YAEA,OAAO;QACX,EAAE,OAAO,OAAO;YACZ,QAAQ,KAAK,CAAC,sBAAsB;YACpC,MAAM,IAAI,MAAM;QACpB;IACJ;IAEA,aAAa,iBACT,KAAa,EACb,UAAwD,EACzB;QAC/B,MAAM,WAAW,IAAI;QAErB,MAAM,OAAO,CAAC,CAAC;YACX,SAAS,MAAM,CAAC,SAAS;QAC7B;QAEA,IAAI;YACA,MAAM,WAAW,MAAM,UAAU,IAAI,CACjC,6BACA,UACA;gBACI,SAAS;oBACL,gBAAgB;gBACpB;gBACA,kBAAkB;YACtB;YAGJ,OAAO,SAAS,IAAI,CAAC,IAAI,IAAI,EAAE;QACnC,EAAE,OAAO,OAAO;YACZ,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,MAAM;QACV;IACJ;IAEA,aAAa,6BACT,KAAa,EACb,UAAiD,EAClB;QAC/B,IAAI;YACA,MAAM,SAAS,MAAM,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC;gBAC/C,IAAI,cAAc,cAAc,KAAK,EAAE;oBACnC,MAAM,WAAW,KAAK,KAAK,CACvB,AAAC,cAAc,MAAM,GAAG,MAAO,cAAc,KAAK;oBAGtD,MAAM,gBAAkC,MAAM,GAAG,CAAC,CAAC,MAAM,QAAU,CAAC;4BAChE,QAAQ,GAAG,KAAK,IAAI,CAAC,CAAC,EAAE,OAAO;4BAC/B,UAAU;4BACV,QAAQ,aAAa,MAAM,cAAc;wBAC7C,CAAC;oBAED,WAAW;gBACf;YACJ;YAEA,OAAO;QACX,EAAE,OAAO,OAAO;YACZ,QAAQ,KAAK,CAAC,sBAAsB;YAEpC,kCAAkC;YAClC,IAAI,iBAAiB,OAAO;gBACxB,MAAM;YACV;YAEA,+CAA+C;YAC/C,IAAI,SAAS,OAAO,UAAU,YAAY,cAAc,OAAO;gBAC3D,MAAM,aAAa;gBACnB,MAAM,UAAU,WAAW,QAAQ,EAAE,MAAM,WACvC,WAAW,QAAQ,EAAE,MAAM,SAC3B,WAAW,OAAO,IAClB;gBACJ,MAAM,IAAI,MAAM;YACpB;YAEA,MAAM,IAAI,MAAM;QACpB;IACJ;IAEA,OAAO,aAAa,IAAU,EAAwC;QAClE,MAAM,UAAU,MAAM,OAAO,MAAM,QAAQ;QAE3C,IAAI,KAAK,IAAI,GAAG,SAAS;YACrB,OAAO;gBACH,SAAS;gBACT,OAAO,CAAC,MAAM,EAAE,KAAK,IAAI,CAAC,gCAAgC,EAAE,IAAI,CAAC,cAAc,CAAC,KAAK,IAAI,EAAE,gCAAgC,CAAC;YAChI;QACJ;QAEA,MAAM,eAAe;YACjB,SAAS;YACT;YAAc;YAAa;YAAa;YAAc;YAAiB;YAAa;YACpF,SAAS;YACT;YAAa;YAAc;YAAa;YAAa;YAAa;YAAa;YAAa;YAC5F,QAAQ;YACR;YAAa;YAAa;YAAa;YAAa;YAAc;YAAa;YAC/E,YAAY;YACZ;YAAmB;YACnB;YACA;YAAmB;YACnB,eAAe;YACf;YACA;YACA;YACA,gBAAgB;YAChB;YACA;YACA;YACA,WAAW;YACX;YAAmB;YAAgC;YAA+B;YAAqB;YACvG,OAAO;YACP;YAAc;YAAY;YAAa;YAAY;YAAmB;YAAY;YAAoB;SACzG;QAED,IAAI,CAAC,aAAa,QAAQ,CAAC,KAAK,IAAI,GAAG;YACnC,MAAM,QAAQ,CAAC,SAAS,EAAE,KAAK,IAAI,CAAC,+DAA+D,CAAC;YACpG,OAAO;gBACH,SAAS;gBACT;YACJ;QACJ;QAEA,OAAO;YAAE,SAAS;QAAK;IAC3B;IAEA,OAAO,cAAc,KAAa,EAA0C;QACxE,MAAM,WAAW;QACjB,MAAM,SAAmB,EAAE;QAE3B,IAAI,MAAM,MAAM,GAAG,UAAU;YACzB,OAAO,IAAI,CAAC,CAAC,WAAW,EAAE,SAAS,sDAAsD,CAAC;QAC9F;QAEA,MAAM,OAAO,CAAC,CAAC,MAAM;YACjB,MAAM,aAAa,IAAI,CAAC,YAAY,CAAC;YACrC,IAAI,CAAC,WAAW,OAAO,EAAE;gBACrB,OAAO,IAAI,CAAC,WAAW,KAAK,IAAI,CAAC,OAAO,EAAE,QAAQ,EAAE,EAAE,EAAE,KAAK,IAAI,CAAC,eAAe,CAAC;YACtF;QACJ;QAEA,OAAO;YACH,SAAS,OAAO,MAAM,KAAK;YAC3B;QACJ;IACJ;IAEA,OAAO,eAAe,KAAa,EAAU;QACzC,IAAI,UAAU,GAAG,OAAO;QAExB,MAAM,IAAI;QACV,MAAM,QAAQ;YAAC;YAAS;YAAM;YAAM;SAAK;QACzC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;QAEhD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;IAC3E;IAEA,OAAO,mBAAmB,IAAU,EAAmB;QACnD,OAAO,IAAI,QAAQ,CAAC,SAAS;YACzB,IAAI,CAAC,KAAK,IAAI,CAAC,UAAU,CAAC,WAAW;gBACjC,OAAO,IAAI,MAAM;gBACjB;YACJ;YAEA,MAAM,SAAS,IAAI;YACnB,OAAO,MAAM,GAAG,CAAC;gBACb,QAAQ,EAAE,MAAM,EAAE;YACtB;YACA,OAAO,OAAO,GAAG;gBACb,OAAO,IAAI,MAAM;YACrB;YACA,OAAO,aAAa,CAAC;QACzB;IACJ;AACJ;uCAEe", "debugId": null}}, {"offset": {"line": 487, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Chat-Service/chat-message-web-app/chat-message/src/app/auth/login/page.tsx"], "sourcesContent": ["'use client';\r\nimport { useEffect, useState } from 'react';\r\nimport { useRouter } from 'next/navigation';\r\nimport Link from 'next/link';\r\nimport toast from 'react-hot-toast';\r\nimport { ApiService } from '@/api/axios';\r\n\r\nexport default function LoginPage() {\r\n    const [email, setEmail] = useState('');\r\n    const [password, setPassword] = useState('');\r\n    const router = useRouter();\r\n\r\n    const handleSubmit = async (e: React.FormEvent) => {\r\n        e.preventDefault();\r\n        try {\r\n            const data = await ApiService.login(email, password);\r\n            localStorage.setItem('accessToken', data.accessToken);\r\n            localStorage.setItem('refreshToken', data.refreshToken);\r\n            toast.success('Đăng nhập thành công! Đang chuyển hướng...', {\r\n                duration: 500,\r\n            });\r\n            setTimeout(() => router.push('/chat'), 500);\r\n        } catch (error: any) {\r\n            const errorMessage = error.response?.data?.message || 'Đăng nhập thất bại';\r\n            toast.error(errorMessage, {\r\n                duration: 3000,\r\n            });\r\n        }\r\n    };\r\n\r\n    const getDeviceId = () => {\r\n        let deviceId = localStorage.getItem('deviceId');\r\n\r\n        if (!deviceId) {\r\n            const userAgent = window.navigator.userAgent;\r\n            const language = window.navigator.language;\r\n            const screenResolution = `${window.screen.width}x${window.screen.height}`;\r\n            const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;\r\n            const timestamp = Date.now();\r\n\r\n            const randomComponent = Math.random().toString(36).substring(2, 15) +\r\n                Math.random().toString(36).substring(2, 15);\r\n\r\n            const fingerprint = btoa(\r\n                `${userAgent}-${language}-${screenResolution}-${timezone}-${timestamp}-${randomComponent}`\r\n            ).replace(/[^a-zA-Z0-9]/g, '').substring(0, 32);\r\n\r\n            deviceId = `device_${fingerprint}`;\r\n\r\n            localStorage.setItem('deviceId', deviceId);\r\n        }\r\n\r\n        return deviceId;\r\n    }\r\n\r\n    useEffect(() => {\r\n        const deviceId = getDeviceId();\r\n        console.log(\"Device ID:\", deviceId);\r\n    }, []);\r\n\r\n    return (\r\n        <div className=\"min-h-screen flex items-center justify-center bg-gray-100 p-4\">\r\n            <div className=\"max-w-md w-full space-y-8 p-8 bg-white rounded-2xl shadow-xl transform transition-all duration-300\">\r\n                <div className=\"flex flex-col items-center gap-3\">\r\n                    <img src=\"/next.svg\" alt=\"Logo\" className=\"w-16 h-16 mb-2 transform hover:scale-105 transition-transform duration-300\" />\r\n                    <h2 className=\"text-4xl font-bold text-gray-900 tracking-tight\">Đăng Nhập</h2>\r\n                    <p className=\"text-gray-600 text-sm\">Chào mừng bạn trở lại! Hãy đăng nhập để tiếp tục.</p>\r\n                </div>\r\n                <form className=\"space-y-6\" onSubmit={handleSubmit}>\r\n                    <div className=\"space-y-5\">\r\n                        <div>\r\n                            <label htmlFor=\"email\" className=\"block text-sm font-semibold text-gray-800 mb-2\">Email</label>\r\n                            <input\r\n                                id=\"email\"\r\n                                name=\"email\"\r\n                                type=\"email\"\r\n                                required\r\n                                className=\"block w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-gray-500 focus:border-gray-500 bg-white text-gray-900 placeholder-gray-400 transition-all duration-200\"\r\n                                placeholder=\"Nhập email của bạn\"\r\n                                value={email}\r\n                                onChange={(e) => setEmail(e.target.value)}\r\n                            />\r\n                        </div>\r\n                        <div>\r\n                            <label htmlFor=\"password\" className=\"block text-sm font-semibold text-gray-800 mb-2\">Mật khẩu</label>\r\n                            <input\r\n                                id=\"password\"\r\n                                name=\"password\"\r\n                                type=\"password\"\r\n                                required\r\n                                className=\"block w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-gray-500 focus:border-gray-500 bg-white text-gray-900 placeholder-gray-400 transition-all duration-200\"\r\n                                placeholder=\"Nhập mật khẩu\"\r\n                                value={password}\r\n                                onChange={(e) => setPassword(e.target.value)}\r\n                            />\r\n                        </div>\r\n                    </div>\r\n                    <button\r\n                        type=\"submit\"\r\n                        className=\"w-full py-3 rounded-lg bg-gray-900 text-white font-semibold text-lg shadow-lg hover:bg-gray-800 focus:ring-4 focus:ring-gray-300 transition-all duration-300 transform hover:scale-105\"\r\n                    >\r\n                        Đăng Nhập\r\n                    </button>\r\n                    <div className=\"text-center text-sm mt-3\">\r\n                        <span className=\"text-gray-600\">Chưa có tài khoản? </span>\r\n                        <Link href=\"/auth/register\" className=\"font-semibold text-gray-700 hover:text-gray-900 hover:underline transition-colors duration-200\">Đăng ký ngay</Link>\r\n                    </div>\r\n                </form>\r\n            </div>\r\n        </div>\r\n    );\r\n}"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AALA;;;;;;;AAOe,SAAS;IACpB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,eAAe,OAAO;QACxB,EAAE,cAAc;QAChB,IAAI;YACA,MAAM,OAAO,MAAM,mHAAA,CAAA,aAAU,CAAC,KAAK,CAAC,OAAO;YAC3C,aAAa,OAAO,CAAC,eAAe,KAAK,WAAW;YACpD,aAAa,OAAO,CAAC,gBAAgB,KAAK,YAAY;YACtD,uJAAA,CAAA,UAAK,CAAC,OAAO,CAAC,8CAA8C;gBACxD,UAAU;YACd;YACA,WAAW,IAAM,OAAO,IAAI,CAAC,UAAU;QAC3C,EAAE,OAAO,OAAY;YACjB,MAAM,eAAe,MAAM,QAAQ,EAAE,MAAM,WAAW;YACtD,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC,cAAc;gBACtB,UAAU;YACd;QACJ;IACJ;IAEA,MAAM,cAAc;QAChB,IAAI,WAAW,aAAa,OAAO,CAAC;QAEpC,IAAI,CAAC,UAAU;YACX,MAAM,YAAY,OAAO,SAAS,CAAC,SAAS;YAC5C,MAAM,WAAW,OAAO,SAAS,CAAC,QAAQ;YAC1C,MAAM,mBAAmB,GAAG,OAAO,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,OAAO,MAAM,CAAC,MAAM,EAAE;YACzE,MAAM,WAAW,KAAK,cAAc,GAAG,eAAe,GAAG,QAAQ;YACjE,MAAM,YAAY,KAAK,GAAG;YAE1B,MAAM,kBAAkB,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG,MAC5D,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG;YAE5C,MAAM,cAAc,KAChB,GAAG,UAAU,CAAC,EAAE,SAAS,CAAC,EAAE,iBAAiB,CAAC,EAAE,SAAS,CAAC,EAAE,UAAU,CAAC,EAAE,iBAAiB,EAC5F,OAAO,CAAC,iBAAiB,IAAI,SAAS,CAAC,GAAG;YAE5C,WAAW,CAAC,OAAO,EAAE,aAAa;YAElC,aAAa,OAAO,CAAC,YAAY;QACrC;QAEA,OAAO;IACX;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,MAAM,WAAW;QACjB,QAAQ,GAAG,CAAC,cAAc;IAC9B,GAAG,EAAE;IAEL,qBACI,8OAAC;QAAI,WAAU;kBACX,cAAA,8OAAC;YAAI,WAAU;;8BACX,8OAAC;oBAAI,WAAU;;sCACX,8OAAC;4BAAI,KAAI;4BAAY,KAAI;4BAAO,WAAU;;;;;;sCAC1C,8OAAC;4BAAG,WAAU;sCAAkD;;;;;;sCAChE,8OAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;8BAEzC,8OAAC;oBAAK,WAAU;oBAAY,UAAU;;sCAClC,8OAAC;4BAAI,WAAU;;8CACX,8OAAC;;sDACG,8OAAC;4CAAM,SAAQ;4CAAQ,WAAU;sDAAiD;;;;;;sDAClF,8OAAC;4CACG,IAAG;4CACH,MAAK;4CACL,MAAK;4CACL,QAAQ;4CACR,WAAU;4CACV,aAAY;4CACZ,OAAO;4CACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;8CAGhD,8OAAC;;sDACG,8OAAC;4CAAM,SAAQ;4CAAW,WAAU;sDAAiD;;;;;;sDACrF,8OAAC;4CACG,IAAG;4CACH,MAAK;4CACL,MAAK;4CACL,QAAQ;4CACR,WAAU;4CACV,aAAY;4CACZ,OAAO;4CACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;;;;;;;sCAIvD,8OAAC;4BACG,MAAK;4BACL,WAAU;sCACb;;;;;;sCAGD,8OAAC;4BAAI,WAAU;;8CACX,8OAAC;oCAAK,WAAU;8CAAgB;;;;;;8CAChC,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAiB,WAAU;8CAAiG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM/J", "debugId": null}}]}