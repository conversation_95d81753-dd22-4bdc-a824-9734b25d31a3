"use client";

import React, { useState } from "react";
import { ConversationCreationResponse, ConnectionStatus } from "@/types/chat";
import { WebSocketStatusCompact } from "./WebSocketStatus";
import { CallType } from "@/types/videoCall";
import { videoCallService } from "@/services/videoCallService";
import { getCurrentUserId } from "@/utils/auth";
import VideoCallModal from "./VideoCall/VideoCallModal";

interface ChatHeaderProps {
    conversation: ConversationCreationResponse | undefined;
    connectionStatus: ConnectionStatus;
}

export const ChatHeader: React.FC<ChatHeaderProps> = ({
    conversation,
    connectionStatus
}) => {
    const [isVideoCallModalOpen, setIsVideoCallModalOpen] = useState(false);
    const [currentCallData, setCurrentCallData] = useState(null);
    const [isInitiatingCall, setIsInitiatingCall] = useState(false);
    const currentUserId = getCurrentUserId();

    if (!conversation) {
        return (
            <></>
        );
    }
    const displayName = conversation.conversationName || 'Unknown';
    const displayInfo = `${conversation.participantInfo?.length || 0} participants`;
    const displayInitial = displayName.charAt(0).toUpperCase();

    const handleInitiateCall = async (callType: CallType) => {
        if (!currentUserId || isInitiatingCall) return;

        setIsInitiatingCall(true);
        try {
            const callData = await videoCallService.initiateCall({
                conversationId: conversation.id,
                callerId: currentUserId,
                callType
            });

            setCurrentCallData(callData);
            setIsVideoCallModalOpen(true);
        } catch (error) {
            console.error('Failed to initiate call:', error);
        } finally {
            setIsInitiatingCall(false);
        }
    };

    const handleCloseVideoCall = () => {
        setIsVideoCallModalOpen(false);
        setCurrentCallData(null);
    };

    return (
        <div className="h-16 bg-white/95 backdrop-blur-sm border-b border-slate-200/60 flex items-center justify-between px-6 shadow-sm">
            <div className="flex items-center gap-4">
                <div className="relative">
                    {conversation.conversationAvatar ? (
                        <img
                            src={conversation.conversationAvatar}
                            alt={displayName}
                            className="w-11 h-11 rounded-full object-cover shadow-sm"
                        />
                    ) : (
                        <div className="w-11 h-11 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center text-white font-semibold shadow-sm">
                            {displayInitial}
                        </div>
                    )}
                    <div className="absolute -bottom-0.5 -right-0.5 w-3.5 h-3.5 bg-green-500 rounded-full border-2 border-white shadow-sm"></div>
                </div>
                <div>
                    <h2 className="font-semibold text-slate-900 text-lg">
                        {displayName}
                    </h2>
                    <p className="text-sm text-slate-500 font-medium">
                        {displayInfo} • Active now
                    </p>
                </div>
            </div>

            <div className="flex items-center gap-3">
                {/* WebSocket Connection Status */}
                <WebSocketStatusCompact
                    connectionStatus={connectionStatus}
                    className="animate-pulse"
                />

                <button
                    onClick={() => handleInitiateCall(CallType.AUDIO)}
                    disabled={isInitiatingCall}
                    className="p-2 rounded-lg bg-gray-50 hover:bg-gray-100 text-gray-600 hover:text-gray-700 transition-colors duration-150 disabled:opacity-50 disabled:cursor-not-allowed"
                    title="Voice call"
                >
                    {isInitiatingCall ? (
                        <div className="w-5 h-5 border-2 border-gray-400 border-t-transparent rounded-full animate-spin"></div>
                    ) : (
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                        </svg>
                    )}
                </button>

                <button
                    onClick={() => handleInitiateCall(CallType.VIDEO)}
                    disabled={isInitiatingCall}
                    className="p-2 rounded-lg bg-blue-50 hover:bg-blue-100 text-blue-600 hover:text-blue-700 transition-colors duration-150 disabled:opacity-50 disabled:cursor-not-allowed"
                    title="Video call"
                >
                    {isInitiatingCall ? (
                        <div className="w-5 h-5 border-2 border-blue-400 border-t-transparent rounded-full animate-spin"></div>
                    ) : (
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                        </svg>
                    )}
                </button>

                <button
                    className="p-2 rounded-lg bg-gray-50 hover:bg-gray-100 text-gray-600 hover:text-gray-700 transition-colors duration-150"
                    title="More options"
                >
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
                    </svg>
                </button>
            </div>

            {/* Video Call Modal */}
            <VideoCallModal
                isOpen={isVideoCallModalOpen}
                onClose={handleCloseVideoCall}
                callData={currentCallData}
                conversationId={conversation.id}
                isIncoming={false}
            />
        </div>
    );
};
